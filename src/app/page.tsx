'use client'

import React from 'react'
import { useAppStore } from '@/lib/store'
import MainLayout from '@/components/layout/MainLayout'
import ReflectionLayout from '@/components/layout/ReflectionLayout'
import ModeSwitch from '@/components/layout/ModeSwitch'

export default function Home() {
  const { mode } = useAppStore()

  return (
    <div className="relative">
      {/* 模式切换组件 */}
      <ModeSwitch />

      {/* 主内容区域 - 根据模式显示不同布局 */}
      <div className="ml-16"> {/* 为左侧模式切换栏留出空间 */}
        {mode === 'browse' ? (
          <MainLayout />
        ) : (
          <ReflectionLayout />
        )}
      </div>
    </div>
  )
}


