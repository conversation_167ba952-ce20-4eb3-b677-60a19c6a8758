import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// 获取单个笔记详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const note = await prisma.savedNote.findUnique({
      where: { id: params.id }
    })

    if (!note) {
      return NextResponse.json(
        { error: '笔记不存在' },
        { status: 404 }
      )
    }

    // 处理标签
    const processedNote = {
      ...note,
      tags: note.tags ? JSON.parse(note.tags) : []
    }

    return NextResponse.json(processedNote)

  } catch (error) {
    console.error('Error fetching note:', error)
    return NextResponse.json(
      { error: '获取笔记失败' },
      { status: 500 }
    )
  }
}

// 更新笔记
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const {
      title,
      integratedNotes,
      tags
    } = await request.json()

    const updatedNote = await prisma.savedNote.update({
      where: { id: params.id },
      data: {
        title,
        integratedNotes,
        tags: tags ? JSON.stringify(tags) : null,
        updatedAt: new Date()
      }
    })

    const processedNote = {
      ...updatedNote,
      tags: updatedNote.tags ? JSON.parse(updatedNote.tags) : []
    }

    return NextResponse.json({
      success: true,
      note: processedNote
    })

  } catch (error) {
    console.error('Error updating note:', error)
    return NextResponse.json(
      { error: '更新笔记失败' },
      { status: 500 }
    )
  }
}

// 删除笔记
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.savedNote.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      message: '笔记已删除'
    })

  } catch (error) {
    console.error('Error deleting note:', error)
    return NextResponse.json(
      { error: '删除笔记失败' },
      { status: 500 }
    )
  }
}
