import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const {
      title,
      originalContent,
      structuredNotes,
      integratedNotes,
      sourceType,
      sourceData,
      tags
    } = await request.json()

    // 验证必需字段
    if (!title || !originalContent || !integratedNotes || !sourceType || !sourceData) {
      return NextResponse.json(
        { error: '缺少必需字段' },
        { status: 400 }
      )
    }

    // 保存笔记到数据库
    const savedNote = await prisma.savedNote.create({
      data: {
        title,
        originalContent,
        structuredNotes: structuredNotes || '',
        integratedNotes,
        sourceType,
        sourceData,
        tags: tags ? JSON.stringify(tags) : null
      }
    })

    return NextResponse.json({
      success: true,
      note: savedNote
    })

  } catch (error) {
    console.error('Error saving note:', error)
    return NextResponse.json(
      { error: '保存笔记失败' },
      { status: 500 }
    )
  }
}
