import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // 构建查询条件
    const where = search ? {
      OR: [
        { title: { contains: search, mode: 'insensitive' as const } },
        { integratedNotes: { contains: search, mode: 'insensitive' as const } },
        { tags: { contains: search, mode: 'insensitive' as const } }
      ]
    } : {}

    // 获取笔记列表
    const notes = await prisma.savedNote.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset,
      select: {
        id: true,
        title: true,
        sourceType: true,
        sourceData: true,
        tags: true,
        createdAt: true,
        updatedAt: true,
        // 只返回前100个字符的预览
        integratedNotes: true
      }
    })

    // 处理笔记预览和标签
    const processedNotes = notes.map(note => ({
      ...note,
      preview: note.integratedNotes.substring(0, 100) + (note.integratedNotes.length > 100 ? '...' : ''),
      tags: note.tags ? JSON.parse(note.tags) : []
    }))

    // 获取总数
    const total = await prisma.savedNote.count({ where })

    return NextResponse.json({
      notes: processedNotes,
      total,
      hasMore: offset + limit < total
    })

  } catch (error) {
    console.error('Error fetching notes:', error)
    return NextResponse.json(
      { error: '获取笔记列表失败' },
      { status: 500 }
    )
  }
}
