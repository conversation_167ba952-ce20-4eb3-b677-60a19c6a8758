/* 自定义字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --radius: 0.75rem;
  --background: #FFFFFF;
  --foreground: #0F172A;
  --card: #FFFFFF;
  --card-foreground: #0F172A;
  --popover: #FFFFFF;
  --popover-foreground: #0F172A;
  --primary: #6366F1;
  --primary-foreground: #FFFFFF;
  --primary-50: #EEF2FF;
  --primary-100: #E0E7FF;
  --primary-200: #C7D2FE;
  --primary-300: #A5B4FC;
  --primary-400: #818CF8;
  --primary-500: #6366F1;
  --primary-600: #4F46E5;
  --primary-700: #4338CA;
  --primary-800: #3730A3;
  --primary-900: #312E81;
  --primary-950: #1E1B4B;
  --secondary: #F8FAFC;
  --secondary-foreground: #0F172A;
  --muted: #F1F5F9;
  --muted-foreground: #64748B;
  --accent: #F1F5F9;
  --accent-foreground: #0F172A;
  --destructive: #EF4444;
  --border: #E2E8F0;
  --input: #E2E8F0;
  --ring: #6366F1;

  /* 现代化渐变色 */
  --gradient-from: #6366F1;
  --gradient-via: #8B5CF6;
  --gradient-to: #EC4899;

  /* 品牌色系 */
  --brand-50: #F0F9FF;
  --brand-100: #E0F2FE;
  --brand-200: #BAE6FD;
  --brand-300: #7DD3FC;
  --brand-400: #38BDF8;
  --brand-500: #0EA5E9;
  --brand-600: #0284C7;
  --brand-700: #0369A1;
  --brand-800: #075985;
  --brand-900: #0C4A6E;
  --brand-950: #082F49;

  /* 侧边栏 */
  --sidebar: #FAFAFA;
  --sidebar-foreground: #0F172A;
  --sidebar-primary: #6366F1;
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: #F1F5F9;
  --sidebar-accent-foreground: #0F172A;
  --sidebar-border: #E2E8F0;
  --sidebar-ring: #6366F1;
}

.dark {
  --background: #FFFFFF;
  --foreground: #0F172A;
  --card: #FFFFFF;
  --card-foreground: #0F172A;
  --popover: #FFFFFF;
  --popover-foreground: #0F172A;
  --primary: #6366F1;
  --primary-foreground: #FFFFFF;
  --primary-50: #1E1B4B;
  --primary-100: #312E81;
  --primary-200: #3730A3;
  --primary-300: #4338CA;
  --primary-400: #4F46E5;
  --primary-500: #6366F1;
  --primary-600: #818CF8;
  --primary-700: #A5B4FC;
  --primary-800: #C7D2FE;
  --primary-900: #E0E7FF;
  --primary-950: #EEF2FF;
  --secondary: #F8FAFC;
  --secondary-foreground: #0F172A;
  --muted: #F1F5F9;
  --muted-foreground: #64748B;
  --accent: #F1F5F9;
  --accent-foreground: #0F172A;
  --destructive: #EF4444;
  --border: #E2E8F0;
  --input: #E2E8F0;
  --ring: #6366F1;

  /* 深色模式渐变色 */
  --gradient-from: #4F46E5;
  --gradient-via: #7C3AED;
  --gradient-to: #DB2777;

  /* 深色模式品牌色 */
  --brand-50: #082F49;
  --brand-100: #0C4A6E;
  --brand-200: #075985;
  --brand-300: #0369A1;
  --brand-400: #0284C7;
  --brand-500: #0EA5E9;
  --brand-600: #38BDF8;
  --brand-700: #7DD3FC;
  --brand-800: #BAE6FD;
  --brand-900: #E0F2FE;
  --brand-950: #F0F9FF;

  /* 深色模式侧边栏 */
  --sidebar: #FAFAFA;
  --sidebar-foreground: #0F172A;
  --sidebar-primary: #6366F1;
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: #F1F5F9;
  --sidebar-accent-foreground: #0F172A;
  --sidebar-border: #E2E8F0;
  --sidebar-ring: #6366F1;
}

/* 基础样式 */
body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background-color: white;
  color: #0F172A;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variant-numeric: oldstyle-nums;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 现代化滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgb(203 213 225 / 0.5) transparent;
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgb(203 213 225 / 0.5), rgb(156 163 175 / 0.5));
  border-radius: 3px;
}

*::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgb(156 163 175 / 0.7), rgb(107 114 128 / 0.7));
}

/* 选择文本样式 */
::selection {
  background-color: rgb(99 102 241 / 0.2);
  color: rgb(99 102 241);
}

::-moz-selection {
  background-color: rgb(99 102 241 / 0.2);
  color: rgb(99 102 241);
}

/* 现代化工具类 */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225 / 0.5) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, rgb(203 213 225 / 0.5), rgb(156 163 175 / 0.5));
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, rgb(156 163 175 / 0.7), rgb(107 114 128 / 0.7));
  }

  /* 现代化文本渐变 */
  .text-gradient {
    background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 玻璃拟态效果 */
  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-effect-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* 现代化阴影 */
  .shadow-glow-primary {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }

  .shadow-glow-purple {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }

  .shadow-glow-pink {
    box-shadow: 0 0 20px rgba(236, 72, 153, 0.3);
  }

  /* 动画延迟工具类 */
  .animate-delay-100 {
    animation-delay: 100ms;
  }

  .animate-delay-200 {
    animation-delay: 200ms;
  }

  .animate-delay-300 {
    animation-delay: 300ms;
  }

  .animate-delay-500 {
    animation-delay: 500ms;
  }

  /* Liquid Glass 设计系统 - 优化可读性 */
  .liquid-glass {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(255, 255, 255, 0.9) 50%,
      rgba(255, 255, 255, 0.85) 100%
    );
    backdrop-filter: blur(12px) saturate(120%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.06),
      0 2px 8px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    position: relative;
    transition: all 0.2s ease-out;
  }

  .liquid-glass:hover {
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.08),
      0 3px 10px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.5);
    border-color: rgba(255, 255, 255, 0.4);
  }

  /* 液体面板 - 优化可读性 */
  .liquid-panel {
    background: white;
    border: 1px solid rgba(229, 231, 235, 0.8);
    border-radius: 18px;
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.04),
      0 3px 10px rgba(0, 0, 0, 0.03);
    position: relative;
  }

  /* 静态光谱高光效果 - 移除动画以提升可读性 */
  .spectrum-highlight {
    position: relative;
    overflow: hidden;
  }

  .spectrum-highlight::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.02) 0%,
      rgba(147, 51, 234, 0.02) 25%,
      rgba(236, 72, 153, 0.02) 50%,
      rgba(245, 158, 11, 0.02) 75%,
      rgba(34, 197, 94, 0.02) 100%
    );
    pointer-events: none;
  }

  /* 简化的流体运动特性 - 减少干扰 */
  .fluid-motion {
    transition: all 0.2s ease-out;
  }

  .fluid-motion:hover {
    transform: translateY(-1px);
  }

  /* 简化的凝胶柔韧性 - 减少干扰 */
  .gel-flexibility {
    transition: all 0.15s ease-out;
  }

  .gel-flexibility:active {
    transform: scale(0.99);
  }

  /* 智能适应颜色系统 */
  .adaptive-light {
    background: white;
  }

  .adaptive-dark {
    background: linear-gradient(135deg,
      rgba(15, 23, 42, 0.95) 0%,
      rgba(30, 41, 59, 0.9) 100%
    );
  }

  @media (prefers-color-scheme: dark) {
    .liquid-glass {
      background: white;
      border-color: rgba(229, 231, 235, 0.8);
    }

    .liquid-panel {
      background: white;
      border-color: rgba(229, 231, 235, 0.8);
    }
  }

  /* Bento Grid 样式 - 更新为Liquid Glass风格 */
  .bento-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 20px;
  }

  .bento-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  /* 现代化卡片阴影 */
  .card-shadow-soft {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.08);
  }

  .card-shadow-medium {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 2px 6px rgba(0, 0, 0, 0.12);
  }

  .card-shadow-strong {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 12px rgba(0, 0, 0, 0.16);
  }

  /* 文本截断工具类 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* 加载动画 */
@keyframes dot-flashing {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.dot-flashing {
  animation: dot-flashing 1.4s infinite ease-in-out;
}

.dot-flashing:nth-child(2) {
  animation-delay: 0.2s;
}

.dot-flashing:nth-child(3) {
  animation-delay: 0.4s;
}

/* 现代化脉冲动画 */
@keyframes modern-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.modern-pulse {
  animation: modern-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 渐入动画 */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out;
}

/* 卡片展开动画 */
@keyframes card-expand {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 2000px;
    opacity: 1;
  }
}

.card-expand {
  animation: card-expand 0.3s ease-out;
}

/* 骨架屏动画 */
@keyframes skeleton-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.skeleton {
  animation: skeleton-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  background-color: rgb(229 231 235);
  border-radius: 0.375rem;
}

/* 打字机效果 */
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.typing-effect {
  overflow: hidden;
  border-right: 2px solid #3B82F6;
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: #3B82F6;
  }
}

/* 增强 prose 样式 */
.prose {
  color: #111827;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: #111827;
  font-weight: 600;
}

.prose p {
  color: #111827;
  line-height: 1.625;
}

.prose a {
  color: #2563eb;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.prose a:hover {
  text-decoration: underline;
}

.prose strong {
  color: #111827;
  font-weight: 600;
}

.prose em {
  color: #111827;
  font-style: italic;
}

.prose code {
  color: #2563eb;
  background-color: #f3f4f6;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  border: 1px solid #e5e7eb;
}

.prose pre {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
}

.prose pre code {
  background-color: transparent;
  border: 0;
  padding: 0;
}

.prose blockquote {
  color: #6b7280;
  border-left: 4px solid #bfdbfe;
  padding-left: 1.5rem;
  font-style: italic;
  background-color: #f9fafb;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 0 0.25rem 0.25rem 0;
}

.prose ul,
.prose ol {
  color: #111827;
}

.prose li {
  color: #111827;
  line-height: 1.625;
}

.prose img {
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  max-width: 100%;
  height: auto;
}

.prose hr {
  border-color: #e5e7eb;
  margin: 2rem 0;
  border-top-width: 2px;
}

.prose table {
  border-collapse: collapse;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.prose th {
  border: 1px solid #e5e7eb;
  background-color: #f3f4f6;
  padding: 0.75rem;
  font-weight: 600;
  text-align: left;
}

.prose td {
  border: 1px solid #e5e7eb;
  padding: 0.75rem;
  vertical-align: top;
}

/* 结构化笔记专用样式 - 现代化AI笔记软件界面标准 */
.ai-note-content {
  line-height: 1.7;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variant-numeric: oldstyle-nums;
}

.ai-note-content h2 {
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ai-note-content h2:first-child {
  margin-top: 0;
}

.ai-note-content h3 {
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

.ai-note-content ul {
  margin: 0.75rem 0;
  padding-left: 1.25rem;
}

.ai-note-content li {
  margin: 0.375rem 0;
  line-height: 1.6;
  color: #4b5563;
}

.ai-note-content li::marker {
  color: #6366f1;
}

.ai-note-content p {
  margin: 0.75rem 0;
  color: #4b5563;
  line-height: 1.6;
}

.ai-note-content strong {
  color: #1f2937;
  font-weight: 600;
}

.ai-note-content em {
  color: #6366f1;
  font-style: normal;
  font-weight: 500;
}

.ai-note-content code {
  background-color: #f1f5f9;
  color: #3730a3;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  border: 1px solid #e2e8f0;
}

/* 结构化笔记分割线样式 */
.ai-note-content hr {
  border: none;
  height: 1px;
  background: linear-gradient(to right, transparent, #e5e7eb, transparent);
  margin: 1.5rem 0;
}

/* 结构化笔记引用块样式 */
.ai-note-content blockquote {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-left: 4px solid #6366f1;
  padding: 1rem 1.25rem;
  margin: 1rem 0;
  border-radius: 0 0.5rem 0.5rem 0;
  font-style: normal;
  color: #475569;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

.ai-note-content blockquote:hover {
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
  transform: translateY(-1px);
}

/* 现代化流式输出动画 */
.ai-note-content {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 优化代码块样式 */
.ai-note-content pre {
  background: linear-gradient(135deg, #1e293b, #334155);
  border: 1px solid #475569;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

.ai-note-content pre:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
