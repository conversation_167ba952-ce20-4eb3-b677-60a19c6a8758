'use client'

import React, { useEffect } from 'react'
import { useAppStore } from '@/lib/store'
import NotesTree from '@/components/reflection/NotesTree'
import NoteDetail from '@/components/reflection/NoteDetail'
import ReflectionAIAssistant from '@/components/reflection/ReflectionAIAssistant'

const ReflectionLayout: React.FC = () => {
  const { loadSavedNotes, notesLoading } = useAppStore()

  // 组件挂载时加载笔记
  useEffect(() => {
    loadSavedNotes()
  }, [loadSavedNotes])

  return (
    <div className="h-screen flex bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* 左侧栏 - 笔记目录树 (25%) */}
      <div className="w-1/4 min-w-[300px] bg-white border-r border-gray-200/50 shadow-sm">
        <NotesTree />
      </div>

      {/* 中间栏 - 笔记详情 (50%) */}
      <div className="flex-1 bg-white border-r border-gray-200/50">
        <NoteDetail />
      </div>

      {/* 右侧栏 - AI助手 (25%) */}
      <div className="w-1/4 min-w-[300px] bg-white">
        <ReflectionAIAssistant />
      </div>
    </div>
  )
}

export default ReflectionLayout
