'use client'

import React, { useState } from 'react'
import { useAppStore, useSelectedNote } from '@/lib/store'
import { Edit3, Save, X, Globe, Type, Calendar, Tag, ExternalLink } from 'lucide-react'
import SafeMarkdown from '@/components/ui/SafeMarkdown'

const NoteDetail: React.FC = () => {
  const selectedNote = useSelectedNote()
  const { updateNote } = useAppStore()
  const [isEditing, setIsEditing] = useState(false)
  const [editTitle, setEditTitle] = useState('')
  const [editContent, setEditContent] = useState('')
  const [editTags, setEditTags] = useState('')
  const [isSaving, setIsSaving] = useState(false)

  // 开始编辑
  const handleStartEdit = () => {
    if (!selectedNote) return
    setEditTitle(selectedNote.title)
    setEditContent(selectedNote.integratedNotes)
    setEditTags(selectedNote.tags.join(', '))
    setIsEditing(true)
  }

  // 取消编辑
  const handleCancelEdit = () => {
    setIsEditing(false)
    setEditTitle('')
    setEditContent('')
    setEditTags('')
  }

  // 保存编辑
  const handleSaveEdit = async () => {
    if (!selectedNote || isSaving) return

    setIsSaving(true)
    try {
      const tags = editTags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0)

      await updateNote(selectedNote.id, {
        title: editTitle,
        integratedNotes: editContent,
        tags
      })

      setIsEditing(false)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      setIsSaving(false)
    }
  }

  if (!selectedNote) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Edit3 className="w-8 h-8 text-gray-400" />
          </div>
          <p className="text-gray-500">选择一个笔记查看详情</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="p-6 border-b border-gray-200/50">
        <div className="flex items-start justify-between mb-4">
          {isEditing ? (
            <input
              type="text"
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              className="flex-1 text-xl font-bold text-gray-900 bg-transparent border-b border-gray-300 focus:outline-none focus:border-blue-500 mr-4"
              placeholder="笔记标题"
            />
          ) : (
            <h1 className="text-xl font-bold text-gray-900 flex-1">{selectedNote.title}</h1>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center space-x-2">
            {isEditing ? (
              <>
                <button
                  onClick={handleSaveEdit}
                  disabled={isSaving}
                  className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors disabled:opacity-50"
                  title="保存"
                >
                  {isSaving ? (
                    <div className="w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save className="w-4 h-4" />
                  )}
                </button>
                <button
                  onClick={handleCancelEdit}
                  className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
                  title="取消"
                >
                  <X className="w-4 h-4" />
                </button>
              </>
            ) : (
              <button
                onClick={handleStartEdit}
                className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
                title="编辑"
              >
                <Edit3 className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* 元信息 */}
        <div className="flex items-center space-x-4 text-sm text-gray-500">
          {/* 来源类型 */}
          <div className="flex items-center space-x-1">
            {selectedNote.sourceType === 'url' ? (
              <Globe className="w-4 h-4" />
            ) : (
              <Type className="w-4 h-4" />
            )}
            <span>{selectedNote.sourceType === 'url' ? '网页' : '文本'}</span>
          </div>

          {/* 创建时间 */}
          <div className="flex items-center space-x-1">
            <Calendar className="w-4 h-4" />
            <span>{new Date(selectedNote.createdAt).toLocaleString('zh-CN')}</span>
          </div>

          {/* 来源链接 */}
          {selectedNote.sourceType === 'url' && (
            <a
              href={selectedNote.sourceData}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 transition-colors"
            >
              <ExternalLink className="w-4 h-4" />
              <span>查看原文</span>
            </a>
          )}
        </div>

        {/* 标签 */}
        {isEditing ? (
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              标签 (用逗号分隔)
            </label>
            <input
              type="text"
              value={editTags}
              onChange={(e) => setEditTags(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500"
              placeholder="输入标签，用逗号分隔"
            />
          </div>
        ) : selectedNote.tags && selectedNote.tags.length > 0 && (
          <div className="mt-4 flex flex-wrap gap-2">
            {selectedNote.tags.map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
              >
                <Tag className="w-3 h-3 mr-1" />
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto p-6">
        {isEditing ? (
          <textarea
            value={editContent}
            onChange={(e) => setEditContent(e.target.value)}
            className="w-full h-full min-h-[400px] p-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 resize-none font-mono text-sm"
            placeholder="编辑笔记内容 (支持 Markdown 格式)"
          />
        ) : (
          <div className="prose prose-sm max-w-none">
            <SafeMarkdown>{selectedNote.integratedNotes}</SafeMarkdown>
          </div>
        )}
      </div>
    </div>
  )
}

export default NoteDetail
